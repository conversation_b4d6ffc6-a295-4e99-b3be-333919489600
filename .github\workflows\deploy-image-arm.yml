# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

# GitHub recommends pinning actions to a commit SHA.
# To get a newer version, you will need to update the SHA.
# You can also reference a tag or branch, but the action may change without warning.

name: Create and publish a Docker image

on:
  push:
    branches: ['master']
  create:
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push-image:
    if: github.repository == 'zhayujie/chatgpt-on-wechat'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v1

      - name: Available platforms
        run: echo ${{ steps.buildx.outputs.platforms }}

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          file: ./docker/Dockerfile.latest
          platforms: linux/arm64
          tags: ${{ steps.meta.outputs.tags }}-arm64
          labels: ${{ steps.meta.outputs.labels }}

      - uses: actions/delete-package-versions@v4
        with:
          package-name: 'chatgpt-on-wechat'
          package-type: 'container'
          min-versions-to-keep: 10
          delete-only-untagged-versions: 'true'
          token: ${{ secrets.GITHUB_TOKEN }}